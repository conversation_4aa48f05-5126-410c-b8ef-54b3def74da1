# NeonPro AI Services - Production Environment Configuration Template
# 
# 🚨 SECURITY INSTRUCTIONS:
# 1. Copy this file to production.env in your deployment environment (NOT in git)
# 2. Replace all placeholder values with actual secrets
# 3. Never commit production.env to version control
# 4. Use secure secret management in production (e.g., Vercel/AWS Secrets Manager)
#
# This template shows the structure needed without exposing sensitive values

# =============================================================================
# APPLICATION ENVIRONMENT
# =============================================================================
NODE_ENV=production
APP_ENV=production
PORT=3000
NEXT_PUBLIC_APP_URL=https://your-domain.com
VERCEL_URL=https://your-app.vercel.app

# =============================================================================
# SUPABASE CONFIGURATION
# =============================================================================
NEXT_PUBLIC_SUPABASE_URL=https://your-project.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_anon_key_here
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key_here
SUPABASE_PROJECT_ID=your_project_id
SUPABASE_ORGANIZATION_ID=your_org_id

# Database
DATABASE_URL=postgresql://postgres:[password]@db.your-project.supabase.co:5432/postgres
DATABASE_DIRECT_URL=postgresql://postgres:[password]@db.your-project.supabase.co:5432/postgres

# =============================================================================
# AI/ML SERVICES CONFIGURATION
# =============================================================================
# OpenAI
OPENAI_API_KEY=sk-...
OPENAI_ORGANIZATION_ID=org-...
OPENAI_MODEL=gpt-4o-mini
OPENAI_MAX_TOKENS=4096
OPENAI_TEMPERATURE=0.7

# Anthropic Claude
ANTHROPIC_API_KEY=sk-ant-api...
ANTHROPIC_MODEL=claude-3-haiku-20240307

# Azure OpenAI
AZURE_OPENAI_API_KEY=your_azure_key
AZURE_OPENAI_ENDPOINT=https://your-resource.openai.azure.com/
AZURE_OPENAI_DEPLOYMENT=gpt-4o-mini
AZURE_OPENAI_API_VERSION=2024-02-15-preview

# =============================================================================
# MONITORING & ANALYTICS
# =============================================================================
# Sentry (Error Tracking) - Use environment variables instead of hardcoding
SENTRY_DSN=https://<EMAIL>/project-id
SENTRY_ENVIRONMENT=production
SENTRY_SAMPLE_RATE=0.1
SENTRY_TRACES_SAMPLE_RATE=0.1

# PostHog (Analytics)
NEXT_PUBLIC_POSTHOG_KEY=phc_...
NEXT_PUBLIC_POSTHOG_HOST=https://app.posthog.com
POSTHOG_ENABLED=true

# DataDog
DATADOG_API_KEY=your_datadog_key
DATADOG_APP_KEY=your_datadog_app_key
DATADOG_SITE=datadoghq.com

# New Relic
NEW_RELIC_LICENSE_KEY=your_license_key
NEW_RELIC_APP_NAME=NeonPro AI Services

# =============================================================================
# SECURITY & ENCRYPTION
# =============================================================================
# Use strong, randomly generated keys
ENCRYPTION_KEY=generate_32_character_key
JWT_SECRET=generate_strong_jwt_secret
SESSION_SECRET=generate_session_secret

# =============================================================================
# THIRD-PARTY INTEGRATIONS
# =============================================================================
# WhatsApp Business API
WHATSAPP_BUSINESS_ACCOUNT_ID=your_business_id
WHATSAPP_ACCESS_TOKEN=your_access_token
WHATSAPP_WEBHOOK_VERIFY_TOKEN=your_webhook_token
WHATSAPP_PHONE_NUMBER_ID=your_phone_id

# Email Service (SendGrid)
SENDGRID_API_KEY=SG.your_sendgrid_key
SENDGRID_FROM_EMAIL=<EMAIL>
SENDGRID_FROM_NAME=Your App Name

# SMS Service (Twilio)
TWILIO_ACCOUNT_SID=AC...
TWILIO_AUTH_TOKEN=your_auth_token
TWILIO_PHONE_NUMBER=+***********

# Payment Gateway (Stripe)
STRIPE_PUBLISHABLE_KEY=pk_live_...
STRIPE_SECRET_KEY=sk_live_...
STRIPE_WEBHOOK_SECRET=whsec_...

# =============================================================================
# COMPLIANCE SETTINGS
# =============================================================================
# LGPD/GDPR
LGPD_COMPLIANCE_ENABLED=true
DATA_RETENTION_DAYS=2555
CONSENT_REQUIRED_FOR_AI=true
AUDIT_TRAIL_ENABLED=true

# Brazilian Healthcare
ANVISA_COMPLIANCE_ENABLED=true
CFM_COMPLIANCE_ENABLED=true
MEDICAL_DATA_ENCRYPTION_REQUIRED=true

# =============================================================================
# BACKUP & STORAGE
# =============================================================================
BACKUP_S3_BUCKET=your-backups-bucket
BACKUP_S3_REGION=sa-east-1
BACKUP_ENCRYPTION_ENABLED=true

# =============================================================================
# 🛡️ SECURITY REMINDERS
# =============================================================================
# 
# Before deploying:
# 1. Generate unique, strong values for all secrets
# 2. Use proper secret management (Vercel Env Vars, AWS Secrets Manager, etc.)
# 3. Enable audit trails and monitoring
# 4. Regularly rotate keys and tokens
# 5. Test all integrations in staging first
# 6. Ensure all third-party services are configured for production
#