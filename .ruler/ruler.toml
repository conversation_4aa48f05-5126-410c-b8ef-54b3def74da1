# Ruler Configuration File - NeonPro AI Healthcare Platform
# See https://ai.intellectronica.net/ruler for documentation.

# OPTIMIZED STRATEGY: Only APEX Healthcare Agents
# GitHub Copilot automatically loads claude, copilot, and trae configs
# Focus on specialized healthcare agents with contextual loading

# Default: claude agent (uses APEX healthcare configuration)
default_agents = ["claude"]

# --- Claude Agent with APEX Healthcare Configuration ---
[agents.claude]
enabled = true
output_path = ".claude/agents/apex-dev.md"
source_path = ".ruler/agents/apex-dev.md"
description = "APEX Healthcare Development with Constitutional Excellence"

# --- Global .gitignore Configuration ---
[gitignore]
enabled = true

# --- MCP Server Configuration ---
[mcp]
enabled = true
merge_strategy = "merge"

# --- Research & Intelligence MCP Servers ---
[mcp_servers.exa]
command = "npx"
args = ["exa-mcp-server@latest"]
type = "stdio"
env = { EXA_API_KEY = "fae6582d-4562-45be-8ce9-f6c0c3518c66" }

[mcp_servers.sequential-thinking]
command = "npx"
args = ["@modelcontextprotocol/server-sequential-thinking@latest"]
type = "stdio"

[mcp_servers.tavily]
command = "npx"
args = ["@mcptools/mcp-tavily@latest"]
type = "stdio"
env = { TAVILY_API_KEY = "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" }

[mcp_servers.context7]
command = "npx"
args = ["@upstash/context7-mcp@latest"]
type = "stdio"
env = { UPSTASH_CONTEXT7_API_KEY = "ctx7_fzqcQNgU3AChDBMjNIVYg4zLQp4LgBjZnbA" }

# --- Development & Infrastructure MCP Servers ---
[mcp_servers.desktop-commander]
command = "npx"
args = ["@wonderwhy-er/desktop-commander@latest"]

[mcp_servers.supabase-mcp]
command = "npx"
args = ["@supabase/mcp-server-supabase@latest"]
env = {
    SUPABASE_URL = "https://ownkoxryswokcdanrdgj.supabase.co",
    SUPABASE_ACCESS_TOKEN = "********************************************",
    SUPABASE_PROJECT_REF = "ownkoxryswokcdanrdgj",
    SUPABASE_PROJECT_ID = "ownkoxryswokcdanrdgj",
    SUPABASE_TIMEOUT = "30000"
}

[mcp_servers.shadcn-ui]
command = "npx"
args = ["@jpisnice/shadcn-ui-mcp-server@latest"]
type = "stdio"
env = { GITHUB_PERSONAL_ACCESS_TOKEN = "****************************************" }

# --- HTTP-based MCP Servers ---
[mcp_servers.Vercel]
url = "https://mcp.vercel.com"
type = "http"

[mcp_servers.Sentry]
url = "https://mcp.sentry.dev/mcp/grupous/javascript-nextjs"

# --- Local MCP Servers ---
[mcp_servers.archon]
command = "npx"
args = ["mcp-remote", "http://localhost:8051/mcp"]

[mcp_servers.serena]
type = "stdio"
command = "uvx"
args = [
    "--from",
    "git+https://github.com/oraios/serena",
    "serena",
    "start-mcp-server",
    "--context",
    "ide-assistant",
    "--project",
    "${workspaceFolder}"
]
env = {}
