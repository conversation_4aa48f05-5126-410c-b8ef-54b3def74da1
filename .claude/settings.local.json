{"$schema": "https://json.schemastore.org/claude-code-settings.json", "permissions": {"allow": ["TodoWrite", "mcp__desktop-commander__list_directory", "mcp__desktop-commander__read_file", "mcp__desktop-commander__search_code", "mcp__desktop-commander__start_process", "mcp__desktop-commander__read_process_output", "mcp__desktop-commander__write_file", "mcp__desktop-commander__get_file_info", "mcp__desktop-commander__move_file", "mcp__desktop-commander__edit_block", "mcp__desktop-commander__search_files", "mcp__desktop-commander__create_directory", "mcp__desktop-commander__force_terminate", "mcp__desktop-commander__list_sessions", "mcp__desktop-commander__interact_with_process", "mcp__context7__get-library-docs", "mcp__tavily__searchQNA", "mcp__tavily__searchContext", "mcp__exa__web_search_exa", "mcp__exa__deep_researcher_start", "mcp__exa__deep_researcher_check", "mcp__sequential-thinking__sequentialthinking", "mcp__serena__search_for_pattern", "mcp__serena__find_file", "mcp__serena__list_dir", "mcp__supabase-mcp__list_projects", "mcp__supabase-mcp__execute_sql", "mcp__supabase-mcp__get_project", "mcp__supabase-mcp__get_project_url", "mcp__supabase-mcp__get_anon_key", "mcp__supabase-mcp__list_tables", "mcp__supabase-mcp__apply_migration", "mcp__tavily__tavily-search", "mcp__shadcn-ui__get_component", "mcp__context7__resolve-library-id", "mcp__serena__find_symbol", "mcp__serena__get_symbols_overview", "mcp__supabase-mcp__get_advisors", "mcp__supabase-mcp__get_logs", "mcp__shadcn-ui__list_components", "mcp__shadcn-ui__get_component_demo", "mcp__shadcn-ui__get_component_metadata", "mcp__serena__read_memory", "mcp__ide__executeCode", "mcp__serena__check_onboarding_performed", "mcp__serena__replace_symbol_body", "mcp__serena__insert_before_symbol", "mcp__serena__insert_after_symbol", "mcp___21st-dev_magic__21st_magic_component_inspiration", "mcp___21st-dev_magic__21st_magic_component_builder", "mcp__exa__crawling_exa", "mcp__archon__manage_project", "mcp__archon__manage_task", "mcp__serena__write_memory", "mcp__desktop-commander__get_config", "mcp__archon__get_available_sources", "mcp__archon__perform_rag_query", "mcp__archon__search_code_examples", "mcp__archon__manage_document", "mcp__serena__list_memories", "mcp__tavily__tavily-extract", "mcp__archon__health_check", "mcp__archon__session_info", "mcp__archon__list_tasks", "mcp__archon__get_task", "mcp__archon__create_task", "mcp__archon__list_projects", "mcp__archon__get_project", "mcp__archon__create_project", "mcp__archon__create_document", "mcp__archon__create_version", "mcp__archon__list_documents", "mcp__archon__update_document", "mcp__archon__get_document", "mcp__browsermcp__browser_navigate", "mcp__browsermcp__browser_wait", "mcp__browsermcp__browser_click", "mcp__browsermcp__browser_type", "mcp__browsermcp__browser_snapshot", "mcp__browsermcp__browser_get_console_logs", "mcp__browsermcp__browser_screenshot", "mcp__browsermcp__browser_press_key", "mcp__browsermcp__browser_go_back", "mcp__supabase-mcp__search_docs", "mcp__vercel__get_project", "mcp__vercel__list_teams", "mcp__vercel__list_projects", "mcp__archon__update_task", "mcp__Sequential_Thinking__sequentialthinking", "mcp__<PERSON><PERSON>__tavily-extract", "mcp__<PERSON><PERSON>__tavily-search", "mcp__archon__update_project", "mcp__serena__find_referencing_symbols"], "deny": ["Edit", "Read", "Write", "<PERSON><PERSON>", "NotebookRead", "NotebookEdit", "WebSearch", "WebFetch"]}, "hooks": {"PostToolUse": [{"matcher": "mcp__desktop-commander__write_file|mcp__desktop-commander__edit_block", "hooks": [{"type": "command", "command": "$CLAUDE_PROJECT_DIR/.claude/hooks/format-and-lint.py", "timeout": 30}]}], "PreToolUse": [{"matcher": "mcp__desktop-commander__write_file|mcp__desktop-commander__edit_block", "hooks": [{"type": "command", "command": "$CLAUDE_PROJECT_DIR/.claude/hooks/file-validator.py", "timeout": 10}]}], "UserPromptSubmit": [{"hooks": [{"type": "command", "command": "$CLAUDE_PROJECT_DIR/.claude/hooks/workflow-coordinator.py", "timeout": 15}]}], "SessionStart": [{"hooks": [{"type": "command", "command": "$CLAUDE_PROJECT_DIR/.claude/hooks/session-context.py", "timeout": 10}]}]}, "hookConfiguration": {"enableProactiveCommands": true, "enablePlanTracking": true, "enableErrorRecovery": true, "maxRetries": 3, "defaultTimeout": 60, "logLevel": "info", "errorThreshold": 5, "planCompletionDetection": {"enabled": true, "inactivityTimeout": 300, "minimumCompletionIndicators": 3}, "commandExecution": {"parallelExecution": true, "environmentIsolation": true, "timeoutGracePeriod": 10, "failureIsolation": true}}, "memory": {"autoLoadClaude": true, "recursiveSearch": true, "projectContextPath": ".claude", "masterConfigFile": "CLAUDE.md", "loadOnStartup": true}, "parallelTasksCount": 5}