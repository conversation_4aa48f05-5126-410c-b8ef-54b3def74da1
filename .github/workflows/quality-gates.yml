name: Quality Gates

on:
  pull_request:
    branches: [ main, develop ]
  push:
    branches: [ main ]

jobs:
  quality-gates:
    name: Quality Gates Enforcement
    runs-on: ubuntu-latest
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '20'
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Comprehensive quality assessment
        id: quality-assessment
        run: |
          echo "📊 Running comprehensive quality assessment..."
          
          # Initialize counters
          TOTAL_SCORE=0
          MAX_SCORE=100
          
          # 1. TypeScript Compilation (25 points)
          echo "🔍 Checking TypeScript compilation..."
          if npx tsc --noEmit --skipLibCheck > /dev/null 2>&1; then
            TSC_SCORE=25
            echo "✅ TypeScript compilation: 25/25 points"
          else
            TSC_SCORE=0
            echo "❌ TypeScript compilation: 0/25 points"
          fi
          TOTAL_SCORE=$((TOTAL_SCORE + TSC_SCORE))
          
          # 2. Linting Quality (25 points)
          echo "🔍 Checking linting quality..."
          LINT_VIOLATIONS=$(npx oxlint --quiet 2>/dev/null | wc -l || echo "0")
          if [ $LINT_VIOLATIONS -eq 0 ]; then
            LINT_SCORE=25
          elif [ $LINT_VIOLATIONS -le 50 ]; then
            LINT_SCORE=20
          elif [ $LINT_VIOLATIONS -le 100 ]; then
            LINT_SCORE=15
          elif [ $LINT_VIOLATIONS -le 200 ]; then
            LINT_SCORE=10
          else
            LINT_SCORE=5
          fi
          echo "📋 Linting violations: $LINT_VIOLATIONS (Score: $LINT_SCORE/25)"
          TOTAL_SCORE=$((TOTAL_SCORE + LINT_SCORE))
          
          # 3. Type Safety (25 points)
          echo "🔍 Checking type safety..."
          ANY_FILES=$(find . -name "*.ts" -o -name "*.tsx" | grep -v node_modules | grep -v .next | grep -v dist | xargs grep -l ": any" 2>/dev/null | wc -l || echo "0")
          if [ $ANY_FILES -eq 0 ]; then
            TYPE_SCORE=25
          elif [ $ANY_FILES -le 10 ]; then
            TYPE_SCORE=20
          elif [ $ANY_FILES -le 30 ]; then
            TYPE_SCORE=15
          elif [ $ANY_FILES -le 60 ]; then
            TYPE_SCORE=10
          else
            TYPE_SCORE=5
          fi
          echo "🔒 Files with 'any' types: $ANY_FILES (Score: $TYPE_SCORE/25)"
          TOTAL_SCORE=$((TOTAL_SCORE + TYPE_SCORE))
          
          # 4. Security & Best Practices (25 points)
          echo "🔍 Checking security and best practices..."
          SECURITY_ISSUES=$(npx oxlint --quiet 2>/dev/null | grep -c "security\|dangerous" || echo "0")
          if [ $SECURITY_ISSUES -eq 0 ]; then
            SECURITY_SCORE=25
          elif [ $SECURITY_ISSUES -le 5 ]; then
            SECURITY_SCORE=15
          else
            SECURITY_SCORE=5
          fi
          echo "🛡️ Security issues: $SECURITY_ISSUES (Score: $SECURITY_SCORE/25)"
          TOTAL_SCORE=$((TOTAL_SCORE + SECURITY_SCORE))
          
          # Calculate final quality score
          QUALITY_SCORE=$(echo "scale=1; $TOTAL_SCORE / 10" | bc)
          
          echo "total_score=$TOTAL_SCORE" >> $GITHUB_OUTPUT
          echo "quality_score=$QUALITY_SCORE" >> $GITHUB_OUTPUT
          echo "tsc_score=$TSC_SCORE" >> $GITHUB_OUTPUT
          echo "lint_score=$LINT_SCORE" >> $GITHUB_OUTPUT
          echo "type_score=$TYPE_SCORE" >> $GITHUB_OUTPUT
          echo "security_score=$SECURITY_SCORE" >> $GITHUB_OUTPUT
          echo "lint_violations=$LINT_VIOLATIONS" >> $GITHUB_OUTPUT
          echo "any_files=$ANY_FILES" >> $GITHUB_OUTPUT
          echo "security_issues=$SECURITY_ISSUES" >> $GITHUB_OUTPUT
          
          # Create quality gates report
          echo "## 🎯 Quality Gates Report" > quality-gates-report.md
          echo "" >> quality-gates-report.md
          echo "### Overall Quality Score: **$QUALITY_SCORE/10.0**" >> quality-gates-report.md
          echo "" >> quality-gates-report.md
          echo "| Category | Score | Max | Status |" >> quality-gates-report.md
          echo "|----------|-------|-----|--------|" >> quality-gates-report.md
          echo "| TypeScript Compilation | $TSC_SCORE | 25 | $([ $TSC_SCORE -eq 25 ] && echo "✅" || echo "❌") |" >> quality-gates-report.md
          echo "| Linting Quality | $LINT_SCORE | 25 | $([ $LINT_SCORE -ge 20 ] && echo "✅" || echo "⚠️") |" >> quality-gates-report.md
          echo "| Type Safety | $TYPE_SCORE | 25 | $([ $TYPE_SCORE -ge 20 ] && echo "✅" || echo "⚠️") |" >> quality-gates-report.md
          echo "| Security & Best Practices | $SECURITY_SCORE | 25 | $([ $SECURITY_SCORE -eq 25 ] && echo "✅" || echo "❌") |" >> quality-gates-report.md
          echo "" >> quality-gates-report.md
          
          if (( $(echo "$QUALITY_SCORE >= 9.5" | bc -l) )); then
            echo "✅ **Quality gates PASSED** - Excellent code quality!" >> quality-gates-report.md
          elif (( $(echo "$QUALITY_SCORE >= 8.0" | bc -l) )); then
            echo "⚠️ **Quality gates WARNING** - Good quality with room for improvement" >> quality-gates-report.md
          else
            echo "❌ **Quality gates FAILED** - Quality improvements required" >> quality-gates-report.md
          fi

      - name: Enforce quality gates
        run: |
          echo "🚪 Enforcing quality gates..."
          
          QUALITY_SCORE=${{ steps.quality-assessment.outputs.quality_score }}
          TSC_SCORE=${{ steps.quality-assessment.outputs.tsc_score }}
          SECURITY_SCORE=${{ steps.quality-assessment.outputs.security_score }}
          
          echo "Quality Score: $QUALITY_SCORE/10.0"
          
          # Critical gates (must pass)
          if [ $TSC_SCORE -ne 25 ]; then
            echo "❌ CRITICAL: TypeScript compilation must succeed"
            echo "::error::Fix TypeScript compilation errors before merging"
            exit 1
          fi
          
          if [ $SECURITY_SCORE -ne 25 ]; then
            echo "❌ CRITICAL: Security issues detected"
            echo "::error::Resolve security issues before merging"
            exit 1
          fi
          
          # Quality threshold gate
          if (( $(echo "$QUALITY_SCORE < 9.5" | bc -l) )); then
            echo "❌ QUALITY GATE FAILED: Score $QUALITY_SCORE < 9.5 required"
            echo "::error::Quality score must be ≥9.5/10 for merging"
            exit 1
          fi
          
          echo "✅ All quality gates passed!"

      - name: Comment PR with quality gates report
        if: github.event_name == 'pull_request'
        uses: actions/github-script@v7
        with:
          script: |
            const fs = require('fs');
            const report = fs.readFileSync('quality-gates-report.md', 'utf8');
            
            github.rest.issues.createComment({
              issue_number: context.issue.number,
              owner: context.repo.owner,
              repo: context.repo.repo,
              body: report
            });

      - name: Upload quality gates artifacts
        if: always()
        uses: actions/upload-artifact@v4
        with:
          name: quality-gates-report
          path: quality-gates-report.md
          retention-days: 30
