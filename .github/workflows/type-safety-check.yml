name: Type Safety Check

on:
  pull_request:
    branches: [main, develop]
    paths:
      - '**/*.ts'
      - '**/*.tsx'
      - 'tsconfig*.json'
      - 'package*.json'
  push:
    branches: [main]
    paths:
      - '**/*.ts'
      - '**/*.tsx'
      - 'tsconfig*.json'
      - 'package*.json'

jobs:
  type-safety-check:
    name: TypeScript Compilation & Type Safety
    runs-on: ubuntu-latest

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Bun
        uses: oven-sh/setup-bun@v2
        with:
          bun-version: latest

      - name: Install dependencies
        run: bun install

      - name: TypeScript compilation check
        id: tsc-check
        run: |
          echo "🔍 Running TypeScript compilation check..."

          # Run TypeScript compilation
          if bunx tsc --noEmit --skipLibCheck 2>&1 | tee tsc-output.txt; then
            echo "✅ TypeScript compilation successful"
            TSC_EXIT_CODE=0
            COMPILATION_STATUS="success"
          else
            echo "❌ TypeScript compilation failed"
            TSC_EXIT_CODE=1
            COMPILATION_STATUS="failed"
          fi

          # Count errors
          ERROR_COUNT=$(grep -c "error TS" tsc-output.txt || echo "0")

          echo "exit_code=$TSC_EXIT_CODE" >> $GITHUB_OUTPUT
          echo "error_count=$ERROR_COUNT" >> $GITHUB_OUTPUT
          echo "status=$COMPILATION_STATUS" >> $GITHUB_OUTPUT

          # Create type safety report
          echo "## 🔒 Type Safety Check Report" > type-safety-report.md
          echo "" >> type-safety-report.md
          echo "### Compilation Status: **$COMPILATION_STATUS**" >> type-safety-report.md
          echo "" >> type-safety-report.md

          if [ $TSC_EXIT_CODE -eq 0 ]; then
            echo "✅ **All TypeScript files compiled successfully**" >> type-safety-report.md
            echo "" >> type-safety-report.md
            echo "- No type errors detected" >> type-safety-report.md
            echo "- Type safety maintained at 100%" >> type-safety-report.md
          else
            echo "❌ **TypeScript compilation failed**" >> type-safety-report.md
            echo "" >> type-safety-report.md
            echo "- **Error Count**: $ERROR_COUNT" >> type-safety-report.md
            echo "- Type safety compromised" >> type-safety-report.md
            echo "" >> type-safety-report.md
            echo "### 🚨 Compilation Errors" >> type-safety-report.md
            echo "\`\`\`" >> type-safety-report.md
            head -30 tsc-output.txt >> type-safety-report.md
            echo "\`\`\`" >> type-safety-report.md
          fi

      - name: Type safety enforcement
        run: |
          echo "🛡️ Enforcing type safety requirements..."

          TSC_EXIT_CODE=${{ steps.tsc-check.outputs.exit_code }}
          ERROR_COUNT=${{ steps.tsc-check.outputs.error_count }}

          # Critical: Block on compilation errors
          if [ $TSC_EXIT_CODE -ne 0 ]; then
            echo "❌ CRITICAL: TypeScript compilation failed with $ERROR_COUNT errors"
            echo "::error::TypeScript compilation must succeed before merging"
            exit 1
          fi

          echo "✅ Type safety requirements met"

      - name: Run additional type checks
        run: |
          echo "🔍 Running additional type safety checks..."

          # Check for any remaining explicit 'any' types
          ANY_COUNT=$(find . -name "*.ts" -o -name "*.tsx" | grep -v node_modules | grep -v .next | grep -v dist | xargs grep -c ": any" 2>/dev/null | grep -v ":0" | wc -l || echo "0")

          echo "Found $ANY_COUNT files with explicit 'any' types"

          if [ $ANY_COUNT -gt 60 ]; then
            echo "⚠️ WARNING: High number of explicit 'any' types ($ANY_COUNT files)"
            echo "::warning::Consider improving type safety by reducing 'any' usage"
          fi

          echo "any_files_count=$ANY_COUNT" >> $GITHUB_ENV

      - name: Comment PR with type safety report
        if: github.event_name == 'pull_request'
        uses: actions/github-script@v7
        with:
          script: |
            const fs = require('fs');
            const report = fs.readFileSync('type-safety-report.md', 'utf8');

            github.rest.issues.createComment({
              issue_number: context.issue.number,
              owner: context.repo.owner,
              repo: context.repo.repo,
              body: report
            });

      - name: Upload type safety artifacts
        if: always()
        uses: actions/upload-artifact@v4
        with:
          name: type-safety-report
          path: |
            tsc-output.txt
            type-safety-report.md
          retention-days: 30
