// This file configures the initialization of Sentry for edge features (middleware, edge routes, and so on).
// The config you add here will be used whenever one of the edge features is loaded.
// Note that this config is unrelated to the Vercel Edge Runtime and is also required when running locally.
// https://docs.sentry.io/platforms/javascript/guides/nextjs/

import * as Sentry from "@sentry/nextjs";

Sentry.init({
  // 🔒 SECURITY: Use environment variable instead of hardcoding DSN
  dsn: process.env.SENTRY_DSN,

  // Define how likely traces are sampled. Adjust this value in production, or use tracesSampler for greater control.
  tracesSampleRate: parseFloat(process.env.SENTRY_TRACES_SAMPLE_RATE || "0.1"),

  // Enable logs to be sent to Sentry
  _experiments: {
    enableLogs: process.env.SENTRY_ENABLE_LOGS === "true",
  },

  // Setting this option to true will print useful information to the console while you're setting up Sentry.
  debug: process.env.NODE_ENV === "development",

  // Environment name
  environment: process.env.SENTRY_ENVIRONMENT || process.env.NODE_ENV || "development",

  // Add the OpenAI integration
  integrations: [
    Sentry.openAIIntegration({
      recordInputs: process.env.SENTRY_RECORD_AI_INPUTS === "true",
      recordOutputs: process.env.SENTRY_RECORD_AI_OUTPUTS === "true",
    }),
  ],

  // Send default PII data
  sendDefaultPii: process.env.SENTRY_SEND_DEFAULT_PII === "true",
});

// 🛡️ Security Note: 
// Add these environment variables to your deployment environment:
// SENTRY_DSN=https://<EMAIL>/project-id
// SENTRY_ENVIRONMENT=production  
// SENTRY_TRACES_SAMPLE_RATE=0.1
// SENTRY_SEND_DEFAULT_PII=false
// SENTRY_ENABLE_LOGS=true
// SENTRY_RECORD_AI_INPUTS=false (for privacy)
// SENTRY_RECORD_AI_OUTPUTS=false (for privacy)