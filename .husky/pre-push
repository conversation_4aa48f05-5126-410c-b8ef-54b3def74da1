#!/usr/bin/env sh
. "$(dirname -- "$0")/_/husky.sh"

echo "🚀 Running pre-push quality validation..."

# 1. Full TypeScript compilation check
echo "🔍 Running full TypeScript compilation check..."
if ! npx tsc --noEmit --skipLib<PERSON>heck; then
  echo "❌ TypeScript compilation failed"
  echo "💡 Fix all TypeScript errors before pushing"
  exit 1
fi
echo "✅ TypeScript compilation passed"

# 2. Comprehensive quality assessment
echo "🔍 Running comprehensive quality assessment..."

# Count total violations
TOTAL_VIOLATIONS=$(npx oxlint --quiet 2>/dev/null | wc -l || echo "0")
ANY_VIOLATIONS=$(npx oxlint --quiet 2>/dev/null | grep -c "typescript-eslint(no-explicit-any)" || echo "0")
SECURITY_VIOLATIONS=$(npx oxlint --quiet 2>/dev/null | grep -c "security" || echo "0")

echo "📊 Quality Metrics:"
echo "   - Total violations: $TOTAL_VIOLATIONS"
echo "   - Type safety issues: $ANY_VIOLATIONS"
echo "   - Security issues: $SECURITY_VIOLATIONS"

# Critical checks
if [ $SECURITY_VIOLATIONS -gt 0 ]; then
  echo "❌ CRITICAL: Security violations detected ($SECURITY_VIOLATIONS)"
  echo "💡 Resolve all security issues before pushing"
  exit 1
fi

# Quality thresholds
if [ $TOTAL_VIOLATIONS -gt 500 ]; then
  echo "❌ QUALITY GATE FAILED: Too many violations ($TOTAL_VIOLATIONS > 500)"
  echo "💡 Reduce violations before pushing to maintain code quality"
  exit 1
fi

if [ $ANY_VIOLATIONS -gt 100 ]; then
  echo "⚠️ WARNING: High number of 'any' types ($ANY_VIOLATIONS)"
  echo "💡 Consider improving type safety"
fi

# 3. Test execution (if tests exist)
if [ -f "package.json" ] && grep -q '"test"' package.json; then
  echo "🧪 Running tests..."
  if ! npm test; then
    echo "❌ Tests failed"
    echo "💡 Fix failing tests before pushing"
    exit 1
  fi
  echo "✅ Tests passed"
fi

echo ""
echo "✅ All pre-push quality checks passed!"
echo "🚀 Ready to push"
