{"mcpServers": {"exa": {"command": "bunx", "args": ["exa-mcp-server@latest"], "env": {"EXA_API_KEY": "${env:EXA_API_KEY}", "EXA_MAX_RESULTS": "10", "EXA_TIMEOUT": "20000"}, "type": "stdio"}, "sequential-thinking": {"command": "bunx", "args": ["@modelcontextprotocol/server-sequential-thinking@latest"], "env": {"DISABLE_THOUGHT_LOGGING": "false", "SEQUENTIAL_THINKING_MAX_TOKENS": "16000", "SEQUENTIAL_THINKING_THOUGHTS_TO_KEEP": "10", "SEQUENTIAL_THINKING_TIMEOUT": "15000"}, "type": "stdio"}, "tavily": {"command": "bunx", "args": ["@mcptools/mcp-tavily@latest"], "env": {"TAVILY_API_KEY": "${env:TAVILY_API_KEY}", "TAVILY_MAX_RESULTS": "10", "TAVILY_SEARCH_DEPTH": "advanced", "TAVILY_TIMEOUT": "20000"}, "type": "stdio"}, "context7": {"command": "bunx", "args": ["@upstash/context7-mcp@latest"], "env": {"UPSTASH_CONTEXT7_API_KEY": "${env:UPSTASH_CONTEXT7_API_KEY}", "CONTEXT7_MAX_TOKENS": "10000", "CONTEXT7_TIMEOUT": "15000"}, "type": "stdio"}, "desktop-commander": {"command": "docker", "args": ["run", "-i", "--rm", "-v", "/home/<USER>/home/<USER>", "mcp/desktop-commander:latest"], "type": "stdio"}, "supabase": {"command": "bunx", "args": ["@supabase/mcp-server-supabase@latest", "--access-token", "${env:SUPABASE_ACCESS_TOKEN}"], "type": "stdio"}, "shadcn-ui": {"command": "bunx", "args": ["@jpisnice/shadcn-ui-mcp-server@latest"], "env": {"GITHUB_PERSONAL_ACCESS_TOKEN": "${env:GITHUB_PERSONAL_ACCESS_TOKEN}", "SHADCN_CACHE_COMPONENTS": "true", "SHADCN_AUTO_INSTALL": "true"}, "type": "stdio"}, "Vercel": {"url": "https://mcp.vercel.com", "type": "http"}, "archon": {"command": "bunx", "args": ["mcp-remote", "http://localhost:8051/mcp"], "type": "stdio"}, "serena": {"type": "stdio", "command": "uvx", "args": ["--from", "git+https://github.com/oraios/serena", "serena", "start-mcp-server", "--context", "ide-assistant", "--project", "${workspaceFolder}"], "env": {}}}, "inputs": [], "settings": {"globalTimeout": 30000, "retryAttempts": 3, "logLevel": "info"}}