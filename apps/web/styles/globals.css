@tailwind base;
@tailwind components;
@tailwind utilities;

/* Import TweakCN NEONPRO Accessibility Tokens */
@import './accessibility-tokens.css';

/* ===============================================================
   TweakCN NEONPRO Theme Foundation
   Healthcare Design System with Portuguese Optimization
   =============================================================== */

@layer base {
  /* Typography Optimization for Portuguese Medical Content */
  :root {
    /* Portuguese Typography Enhancement */
    --font-sans: "Inter", "Segoe UI", system-ui, sans-serif;
    --font-serif: "Lora", Georgia, serif;
    --font-mono: "JetBrains Mono", Menlo, Monaco, Consolas, monospace;
    
    /* Portuguese Line Height Optimization - 1.65 for better readability */
    --line-height-base: 1.65;
    --line-height-tight: 1.45;
    --line-height-loose: 1.85;
    
    /* Medical Terminology Text Scaling - 1.1x for clarity */
    --text-scale-medical: 1.1;
    --letter-spacing-medical: 0.01em;

    /* Accessibility Focus Ring */
    --focus-ring-width: 2px;
    --focus-ring-offset: 2px;
    
    /* Animation Durations with Reduced Motion Support */
    --animation-duration-fast: 150ms;
    --animation-duration-normal: 300ms;
    --animation-duration-slow: 500ms;
    --animation-easing: cubic-bezier(0.4, 0.0, 0.2, 1);
    
    /* Border Radius System */
    --radius-sm: 0.375rem;
    --radius: 0.5rem;
    --radius-md: 0.75rem;
    --radius-lg: 1rem;
    --radius-xl: 1.5rem;
    
    /* Shadow System for Healthcare UI */
    --shadow-2xs: 0 1px 2px 0 rgb(0 0 0 / 0.05);
    --shadow-xs: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
    --shadow-sm: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
    --shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
    --shadow-md: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
    --shadow-lg: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
    --shadow-xl: 0 25px 50px -12px rgb(0 0 0 / 0.25);
    --shadow-2xl: 0 25px 50px -12px rgb(0 0 0 / 0.25);
  }

  /* ===============================================================
     NEONPRO HEALTHCARE LIGHT THEME (Default)
     =============================================================== */
  :root {
    /* Base Layout Colors */
    --background: 0 0% 100%;
    --foreground: 0 0% 3.9%;
    --card: 0 0% 100%;
    --card-foreground: 0 0% 3.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 0 0% 3.9%;
    
    /* NEONPRO Primary Colors - oklch(142, 71%, 78%) converted to HSL */
    --primary: 142 71% 78%;
    --primary-foreground: 0 0% 9%;
    --primary-light: 142 60% 85%;
    --primary-dark: 142 80% 65%;
    
    /* Secondary & Accent Colors */
    --secondary: 0 0% 96.1%;
    --secondary-foreground: 0 0% 9%;
    --accent: 142 30% 92%;
    --accent-foreground: 142 71% 20%;
    
    /* Utility Colors */
    --muted: 0 0% 96.1%;
    --muted-foreground: 0 0% 45.1%;
    --border: 0 0% 89.8%;
    --input: 0 0% 89.8%;
    --ring: 142 71% 78%;
    
    /* Status Colors with Healthcare Context */
    --success: 142 71% 78%;
    --success-foreground: 0 0% 9%;
    --warning: 45 93% 47%;
    --warning-foreground: 0 0% 9%;
    --info: 217 91% 60%;
    --info-foreground: 0 0% 100%;
    
    /* Emergency Color - oklch(0, 84%, 60%) converted to HSL */
    --destructive: 0 84% 60%;
    --destructive-foreground: 0 0% 98%;
    
    /* Healthcare Vital Signs Colors */
    --status-normal: 142 71% 78%; /* NEONPRO green for normal vitals */
    --status-warning: 45 93% 47%; /* Amber for warning vitals */
    --status-critical: 0 84% 60%; /* Emergency red for critical vitals */
    --status-urgent: 15 90% 53%; /* Orange for urgent attention */
    --status-inactive: 0 0% 63.9%; /* Gray for inactive/disabled */
    
    /* LGPD Compliance Colors - oklch(160, 95%, 30%) converted to HSL */
    --lgpd-compliant: 160 95% 30%;
    --lgpd-warning: 45 93% 47%;
    --lgpd-violation: 0 84% 60%;
    
    /* Chart Colors for Healthcare Analytics */
    --chart-1: 142 71% 78%; /* NEONPRO primary */
    --chart-2: 217 91% 60%; /* Blue */
    --chart-3: 45 93% 47%; /* Amber */
    --chart-4: 15 90% 53%; /* Orange */
    --chart-5: 0 84% 60%; /* Red */
    --chart-6: 285 85% 60%; /* Purple */
    
    /* Sidebar Colors */
    --sidebar-background: 0 0% 98%;
    --sidebar-foreground: 240 5.3% 26.1%;
    --sidebar-primary: 142 71% 78%;
    --sidebar-primary-foreground: 0 0% 9%;
    --sidebar-accent: 142 30% 96%;
    --sidebar-accent-foreground: 142 71% 20%;
    --sidebar-border: 0 0% 89.8%;
    --sidebar-ring: 142 71% 78%;
  }

  /* ===============================================================
     NEONPRO HEALTHCARE DARK THEME
     =============================================================== */
  .dark {
    /* Base Layout Colors */
    --background: 0 0% 3.9%;
    --foreground: 0 0% 98%;
    --card: 0 0% 3.9%;
    --card-foreground: 0 0% 98%;
    --popover: 0 0% 3.9%;
    --popover-foreground: 0 0% 98%;
    
    /* NEONPRO Primary Colors - Darker variants */
    --primary: 142 71% 65%;
    --primary-foreground: 0 0% 98%;
    --primary-light: 142 60% 70%;
    --primary-dark: 142 80% 55%;
    
    /* Secondary & Accent Colors */
    --secondary: 0 0% 14.9%;
    --secondary-foreground: 0 0% 98%;
    --accent: 142 30% 15%;
    --accent-foreground: 142 71% 65%;
    
    /* Utility Colors */
    --muted: 0 0% 14.9%;
    --muted-foreground: 0 0% 63.9%;
    --border: 0 0% 14.9%;
    --input: 0 0% 14.9%;
    --ring: 142 71% 65%;
    
    /* Status Colors - Dark mode variants */
    --success: 142 71% 65%;
    --success-foreground: 0 0% 98%;
    --warning: 45 93% 55%;
    --warning-foreground: 0 0% 9%;
    --info: 217 91% 65%;
    --info-foreground: 0 0% 98%;
    --destructive: 0 84% 55%;
    --destructive-foreground: 0 0% 98%;
    
    /* Healthcare Vital Signs - Dark variants */
    --status-normal: 142 71% 65%;
    --status-warning: 45 93% 55%;
    --status-critical: 0 84% 55%;
    --status-urgent: 15 90% 58%;
    --status-inactive: 0 0% 45.1%;
    
    /* LGPD Compliance - Dark variants */
    --lgpd-compliant: 160 95% 40%;
    --lgpd-warning: 45 93% 55%;
    --lgpd-violation: 0 84% 55%;
    
    /* Chart Colors - Dark mode optimized */
    --chart-1: 142 71% 65%;
    --chart-2: 217 91% 65%;
    --chart-3: 45 93% 55%;
    --chart-4: 15 90% 58%;
    --chart-5: 0 84% 55%;
    --chart-6: 285 85% 65%;
    
    /* Sidebar Colors - Dark mode */
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 142 71% 65%;
    --sidebar-primary-foreground: 0 0% 98%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 142 71% 65%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 142 71% 65%;
  }
  
  /* ===============================================================
     HIGH CONTRAST THEME - WCAG 2.1 AAA (7:1 contrast ratio)
     =============================================================== */
  .high-contrast {
    /* Base Layout Colors - Maximum contrast */
    --background: 0 0% 100%;
    --foreground: 0 0% 0%;
    --card: 0 0% 100%;
    --card-foreground: 0 0% 0%;
    --popover: 0 0% 100%;
    --popover-foreground: 0 0% 0%;
    
    /* Primary Colors - High contrast */
    --primary: 142 100% 25%;
    --primary-foreground: 0 0% 100%;
    --primary-light: 142 100% 35%;
    --primary-dark: 142 100% 15%;
    
    /* Secondary & Accent Colors - High contrast */
    --secondary: 0 0% 90%;
    --secondary-foreground: 0 0% 0%;
    --accent: 142 100% 90%;
    --accent-foreground: 0 0% 0%;
    
    /* Utility Colors - High contrast */
    --muted: 0 0% 85%;
    --muted-foreground: 0 0% 0%;
    --border: 0 0% 0%;
    --input: 0 0% 95%;
    --ring: 142 100% 25%;
    
    /* Status Colors - Maximum contrast for safety */
    --success: 142 100% 25%;
    --success-foreground: 0 0% 100%;
    --warning: 45 100% 35%;
    --warning-foreground: 0 0% 0%;
    --info: 217 100% 30%;
    --info-foreground: 0 0% 100%;
    --destructive: 0 100% 35%;
    --destructive-foreground: 0 0% 100%;
    
    /* Healthcare Vital Signs - High contrast */
    --status-normal: 142 100% 25%;
    --status-warning: 45 100% 35%;
    --status-critical: 0 100% 35%;
    --status-urgent: 15 100% 35%;
    --status-inactive: 0 0% 50%;
    
    /* LGPD Compliance - High contrast */
    --lgpd-compliant: 160 100% 25%;
    --lgpd-warning: 45 100% 35%;
    --lgpd-violation: 0 100% 35%;
    
    /* Chart Colors - High contrast */
    --chart-1: 142 100% 25%;
    --chart-2: 217 100% 30%;
    --chart-3: 45 100% 35%;
    --chart-4: 15 100% 35%;
    --chart-5: 0 100% 35%;
    --chart-6: 285 100% 30%;
    
    /* Sidebar Colors - High contrast */
    --sidebar-background: 0 0% 95%;
    --sidebar-foreground: 0 0% 0%;
    --sidebar-primary: 142 100% 25%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 142 100% 90%;
    --sidebar-accent-foreground: 0 0% 0%;
    --sidebar-border: 0 0% 0%;
    --sidebar-ring: 142 100% 25%;
  }

  /* ===============================================================
     BASE STYLES - Portuguese Typography Optimization
     =============================================================== */
  * {
    @apply border-border;
  }
  
  html {
    font-family: var(--font-sans);
    line-height: var(--line-height-base);
    letter-spacing: var(--letter-spacing-medical);
  }
  
  body {
    @apply bg-background text-foreground;
    font-family: var(--font-sans);
    line-height: var(--line-height-base);
    letter-spacing: var(--letter-spacing-medical);
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }
  
  /* Typography Scale for Portuguese Medical Content */
  h1, h2, h3, h4, h5, h6 {
    font-family: var(--font-sans);
    font-weight: 600;
    line-height: var(--line-height-tight);
    letter-spacing: var(--letter-spacing-medical);
  }
  
  /* Medical terminology gets enhanced scaling */
  .medical-term {
    font-size: calc(1em * var(--text-scale-medical));
    letter-spacing: var(--letter-spacing-medical);
    font-weight: 500;
  }
  
  /* Focus states for accessibility */
  :focus-visible {
    outline: var(--focus-ring-width) solid hsl(var(--ring));
    outline-offset: var(--focus-ring-offset);
  }
  
  /* Reduced motion support */
  @media (prefers-reduced-motion: reduce) {
    :root {
      --animation-duration-fast: 0ms;
      --animation-duration-normal: 0ms;
      --animation-duration-slow: 0ms;
    }
    
    *, *::before, *::after {
      animation-duration: 0.01ms !important;
      animation-iteration-count: 1 !important;
      transition-duration: 0.01ms !important;
    }
  }
  
  /* High contrast mode support */
  @media (prefers-contrast: high) {
    :root:not(.high-contrast) {
      /* Apply high contrast theme automatically */
      --background: 0 0% 100%;
      --foreground: 0 0% 0%;
      --border: 0 0% 0%;
      --ring: 142 100% 25%;
    }
  }
}

@layer utilities {
  .text-balance {
    text-wrap: balance;
  }
  
  /* Healthcare-specific utilities */
  .touch-target {
    min-height: 44px;
    min-width: 44px;
  }
  
  .touch-target-lg {
    min-height: 56px;
    min-width: 56px;
  }
  
  .touch-target-xl {
    min-height: 64px;
    min-width: 64px;
  }
  
  .medical-focus:focus-visible {
    @apply ring-2 ring-ring ring-offset-2;
    box-shadow: 0 0 0 var(--focus-ring-width) hsl(var(--ring) / 0.5);
  }
  
  /* Portuguese typography utilities */
  .text-portuguese {
    line-height: var(--line-height-base);
    letter-spacing: var(--letter-spacing-medical);
  }
  
  .text-medical {
    font-size: calc(1em * var(--text-scale-medical));
    letter-spacing: var(--letter-spacing-medical);
    font-weight: 500;
  }
}