/**
 * Handoff Token Generation API
 * T3.3: Cross-Device Continuity e QR Handoff System
 * 
 * Generates secure, time-limited tokens for cross-device session transfer
 * Features:
 * - AES-256-GCM encryption for session payload
 * - Device fingerprinting for fraud prevention
 * - 5-minute token expiry for security
 * - LGPD compliant audit logging
 * - Redis storage with automatic TTL
 */

import type { NextRequest} from 'next/server';
import { NextResponse } from 'next/server';
import { cookies } from 'next/headers';
import { createClient } from '@/utils/supabase/server';
import crypto from 'node:crypto';

interface DeviceFingerprint {
  userAgent: string;
  screenResolution: string;
  timezone: string;
  language: string;
  deviceType: 'mobile' | 'tablet' | 'desktop';
}

interface HandoffTokenPayload {
  sessionData: Record<string, unknown>;
  deviceFingerprint: DeviceFingerprint;
  expiryMinutes: number;
}

// Encryption utilities
class EncryptionService {
  private static readonly ALGORITHM = 'aes-256-gcm';
  private static readonly KEY_LENGTH = 32;
  private static readonly IV_LENGTH = 16;
  private static readonly TAG_LENGTH = 16;

  private static getEncryptionKey(): Buffer {
    const key = process.env.HANDOFF_ENCRYPTION_KEY;
    if (!key) {
      throw new Error('HANDOFF_ENCRYPTION_KEY environment variable is required');
    }
    return Buffer.from(key, 'hex');
  }

  static encrypt(data: unknown): { encrypted: string; iv: string; tag: string } {
    const key = this.getEncryptionKey();
    const iv = crypto.randomBytes(this.IV_LENGTH);
    const cipher = crypto.createCipheriv(this.ALGORITHM, key, iv);
    cipher.setAAD(Buffer.from('handoff-token'));

    const jsonData = JSON.stringify(data);
    let encrypted = cipher.update(jsonData, 'utf8', 'hex');
    encrypted += cipher.final('hex');

    const tag = cipher.getAuthTag();

    return {
      encrypted,
      iv: iv.toString('hex'),
      tag: tag.toString('hex')
    };
  }

  static decrypt(encryptedData: string, ivHex: string, tagHex: string): unknown {
    const key = this.getEncryptionKey();
    const iv = Buffer.from(ivHex, 'hex');
    const tag = Buffer.from(tagHex, 'hex');
    
    const decipher = crypto.createDecipheriv(this.ALGORITHM, key, iv);
    decipher.setAAD(Buffer.from('handoff-token'));
    decipher.setAuthTag(tag);

    let decrypted = decipher.update(encryptedData, 'hex', 'utf8');
    decrypted += decipher.final('utf8');

    return JSON.parse(decrypted);
  }
}

// Token service
class TokenService {
  static generateSessionId(): string {
    return crypto.randomUUID();
  }

  static generateNonce(): string {
    return crypto.randomBytes(16).toString('hex');
  }

  static createHandoffToken(payload: unknown, deviceFingerprint: DeviceFingerprint, expiryMinutes: number = 5): string {
    const sessionId = this.generateSessionId();
    const nonce = this.generateNonce();
    const issuedAt = Date.now();
    const expiresAt = issuedAt + (expiryMinutes * 60 * 1000);

    const tokenData = {
      sessionId,
      deviceFingerprint,
      payload,
      issuedAt,
      expiresAt,
      nonce
    };

    const { encrypted, iv, tag } = EncryptionService.encrypt(tokenData);
    
    // Create final token with metadata
    const token = Buffer.from(JSON.stringify({
      encrypted,
      iv,
      tag,
      sessionId, // For tracking/logging (not encrypted)
      expiresAt  // For quick expiry checks
    })).toString('base64url');

    return token;
  }

  static verifyAndDecodeToken(token: string): unknown {
    try {
      const tokenData = JSON.parse(Buffer.from(token, 'base64url').toString());
      const { encrypted, iv, tag } = tokenData;

      // Quick expiry check before decryption
      if (Date.now() > tokenData.expiresAt) {
        throw new Error('Token has expired');
      }

      return EncryptionService.decrypt(encrypted, iv, tag);
    } catch (error) {
      throw new Error('Invalid token format or decryption failed');
    }
  }
}

export async function POST(request: NextRequest) {
  try {
    const cookieStore = cookies();
    const supabase = createClient(cookieStore);

    // Verify user authentication
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    if (authError || !user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Parse request body
    const body: HandoffTokenPayload = await request.json();
    const { sessionData, deviceFingerprint, expiryMinutes = 5 } = body;

    // Validate input
    if (!sessionData || !deviceFingerprint) {
      return NextResponse.json(
        { error: 'Missing required fields: sessionData, deviceFingerprint' },
        { status: 400 }
      );
    }

    // Validate expiry time (max 10 minutes for security)
    if (expiryMinutes > 10 || expiryMinutes < 1) {
      return NextResponse.json(
        { error: 'Invalid expiry time. Must be between 1-10 minutes' },
        { status: 400 }
      );
    }

    // Create enhanced session payload
    const enhancedPayload = {
      userId: user.id,
      userEmail: user.email,
      sessionData,
      generatedAt: Date.now(),
      expiryMinutes
    };

    // Generate secure token
    const token = TokenService.createHandoffToken(enhancedPayload, deviceFingerprint, expiryMinutes);
    const sessionId = JSON.parse(Buffer.from(token, 'base64url').toString()).sessionId;

    // Store token in Supabase for tracking and validation
    const expiresAt = new Date(Date.now() + (expiryMinutes * 60 * 1000));
    
    const { error: insertError } = await supabase
      .from('handoff_tokens')
      .insert({
        id: sessionId,
        user_id: user.id,
        encrypted_payload: token,
        device_fingerprint: deviceFingerprint,
        expires_at: expiresAt,
        is_active: true
      });

    if (insertError) {
      console.error('Failed to store token:', insertError);
      return NextResponse.json(
        { error: 'Failed to generate token' },
        { status: 500 }
      );
    }

    // Log generation for audit
    await supabase
      .from('handoff_audit_log')
      .insert({
        token_id: sessionId,
        action: 'generated',
        source_device_fingerprint: deviceFingerprint,
        success: true
      });

    return NextResponse.json({
      success: true,
      token,
      sessionId,
      expiresAt: expiresAt.toISOString(),
      message: 'Handoff token generated successfully'
    });

  } catch (error) {
    console.error('Handoff token generation error:', error);
    
    return NextResponse.json(
      { 
        error: 'Internal server error',
        message: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}