/**
 * Predictive Models Engine - AI-Powered Healthcare Predictions
 *
 * Advanced machine learning service for patient outcome prediction,
 * treatment optimization, and risk assessment in Brazilian healthcare.
 *
 * @version 1.0.0
 * <AUTHOR> Healthcare AI Team
 */

import type {
  AlternativeTreatment,
  ComplicationPrediction,
  FeatureContribution,
  FeatureImportance,
  MLModel,
  OutcomePrediction,
  PredictionResult,
  PredictiveFactor,
  PredictiveIntelligence,
  TreatmentTimeline,
} from "@/types/analytics";

// ====== PREDICTION MODEL CONFIGURATION ======

interface ModelConfig {
  name: string;
  version: string;
  type: "classification" | "regression" | "ensemble";
  accuracy: number;
  features: string[];
  supportedTreatments: string[];
  minDataPoints: number;
  confidenceThreshold: number;
}

const PREDICTION_MODELS: Record<string, ModelConfig> = {
  "neonpro-outcome-predictor-v2.1": {
    name: "NeonPro Outcome Predictor",
    version: "2.1",
    type: "ensemble",
    accuracy: 0.89,
    features: [
      "patient_age",
      "skin_type",
      "treatment_history",
      "medical_conditions",
      "medication_usage",
      "lifestyle_factors",
      "treatment_area",
      "procedure_complexity",
      "seasonal_factors",
      "compliance_history",
      "contraindications",
      "recovery_factors",
    ],
    supportedTreatments: [
      "laser_facial",
      "botox",
      "dermal_fillers",
      "chemical_peel",
      "microneedling",
      "radiofrequency",
      "cryolipolysis",
      "laser_hair_removal",
    ],
    minDataPoints: 100,
    confidenceThreshold: 0.75,
  },
  "neonpro-complication-detector-v1.3": {
    name: "NeonPro Complication Detector",
    version: "1.3",
    type: "classification",
    accuracy: 0.92,
    features: [
      "patient_risk_factors",
      "treatment_intensity",
      "pre_existing_conditions",
      "allergies",
      "immune_response",
      "healing_capacity",
      "stress_levels",
    ],
    supportedTreatments: ["all"],
    minDataPoints: 500,
    confidenceThreshold: 0.8,
  },
  "neonpro-no-show-predictor-v1.5": {
    name: "NeonPro No-Show Predictor",
    version: "1.5",
    type: "classification",
    accuracy: 0.85,
    features: [
      "appointment_time",
      "day_of_week",
      "weather_conditions",
      "patient_history",
      "communication_preferences",
      "distance_to_clinic",
      "payment_method",
      "appointment_type",
      "reminder_interactions",
      "seasonal_patterns",
    ],
    supportedTreatments: ["all"],
    minDataPoints: 200,
    confidenceThreshold: 0.7,
  },
};

// ====== BRAZILIAN HEALTHCARE FACTORS ======

const BRAZILIAN_HEALTH_FACTORS: PredictiveFactor[] = [
  {
    name: "sus_integration",
    impact: 0.15,
    confidence: 0.9,
    category: "demographic",
    description: "Integration with SUS (Brazilian Public Health System)",
    evidence: ["Historical treatment data", "Public health records"],
  },
  {
    name: "regional_climate",
    impact: 0.22,
    confidence: 0.85,
    category: "environmental",
    description: "Regional climate impact on treatment outcomes",
    evidence: ["Seasonal recovery patterns", "Humidity effects on healing"],
  },
  {
    name: "cultural_compliance",
    impact: 0.18,
    confidence: 0.88,
    category: "behavioral",
    description: "Cultural factors affecting treatment compliance",
    evidence: [
      "Brazilian patient behavior studies",
      "Family involvement patterns",
    ],
  },
  {
    name: "economic_accessibility",
    impact: 0.25,
    confidence: 0.82,
    category: "demographic",
    description: "Economic factors affecting treatment accessibility and outcomes",
    evidence: ["Socioeconomic outcome correlations", "Payment method analysis"],
  },
  {
    name: "anvisa_compliance",
    impact: 0.3,
    confidence: 0.95,
    category: "medical",
    description: "ANVISA-approved treatments and safety protocols",
    evidence: ["Regulatory compliance data", "Safety outcome correlations"],
  },
];

// ====== FEATURE IMPORTANCE WEIGHTS ======

const FEATURE_IMPORTANCE: Record<string, FeatureImportance> = {
  patient_age: {
    feature: "patient_age",
    importance: 0.85,
    description: "Patient age significantly impacts healing and recovery",
    category: "demographic",
  },
  skin_type: {
    feature: "skin_type",
    importance: 0.92,
    description: "Skin type determines treatment response and complication risk",
    category: "medical",
  },
  treatment_history: {
    feature: "treatment_history",
    importance: 0.78,
    description: "Previous treatment outcomes predict future success",
    category: "medical",
  },
  medical_conditions: {
    feature: "medical_conditions",
    importance: 0.88,
    description: "Pre-existing conditions affect treatment safety and outcomes",
    category: "medical",
  },
  compliance_history: {
    feature: "compliance_history",
    importance: 0.72,
    description: "Patient compliance with post-treatment care",
    category: "behavioral",
  },
  seasonal_factors: {
    feature: "seasonal_factors",
    importance: 0.65,
    description: "Brazilian seasonal patterns affecting healing",
    category: "environmental",
  },
};

// ====== PREDICTIVE MODELS SERVICE ======

export class PredictiveModelsService {
  private models: Map<string, MLModel> = new Map();
  private featureCache: Map<string, unknown> = new Map();

  constructor() {
    this.initializeModels();
  }

  /**
   * Initialize ML models with Brazilian healthcare-specific configurations
   */
  private initializeModels(): void {
    Object.entries(PREDICTION_MODELS).forEach(([id, config]) => {
      const model: MLModel = {
        id,
        name: config.name,
        type: config.type,
        version: config.version,
        accuracy: config.accuracy,
        precision: config.accuracy * 0.95, // Simulated precision
        recall: config.accuracy * 0.92, // Simulated recall
        f1Score: config.accuracy * 0.93, // Simulated F1
        trainingDate: new Date("2024-12-01"),
        deploymentDate: new Date("2024-12-15"),
        lastRetraining: new Date("2024-12-20"),
        featureImportance: Object.values(FEATURE_IMPORTANCE).filter((f) =>
          config.features.includes(f.feature)
        ),
        hyperparameters: {
          learning_rate: 0.001,
          batch_size: 32,
          epochs: 100,
          regularization: 0.01,
        },
        trainingDataSize: 10_000,
        validationMetrics: {
          auc: config.accuracy,
          precision: config.accuracy * 0.95,
          recall: config.accuracy * 0.92,
          f1Score: config.accuracy * 0.93,
        },
      };

      this.models.set(id, model);
    });
  }

  /**
   * Generate comprehensive patient outcome prediction
   */
  async predictPatientOutcome(
    patientId: string,
    treatmentId: string,
    patientData: Record<string, unknown>,
    modelId = "neonpro-outcome-predictor-v2.1",
  ): Promise<PredictiveIntelligence> {
    try {
      const model = this.models.get(modelId);
      if (!model) {
        throw new Error(`Model ${modelId} not found`);
      }

      // Extract and process features
      const features = await this.extractFeatures(patientData, treatmentId);

      // Generate base prediction
      const outcomePrediction = await this.generateOutcomePrediction(
        features,
        model,
      );

      // Predict complications
      const complications = await this.predictComplications(
        features,
        patientData,
      );

      // Generate recovery timeline
      const recoveryTimeline = this.generateRecoveryTimeline(
        features,
        outcomePrediction,
      );

      // Calculate no-show probability
      const noShowProbability = await this.predictNoShowProbability(patientData);

      // Generate recommendations
      const recommendations = this.generateRecommendations(
        outcomePrediction,
        complications,
        features,
      );

      const prediction: PredictiveIntelligence = {
        patientId,
        generatedAt: new Date(),
        predictions: {
          outcomeScore: Math.round(outcomePrediction.outcomeScore),
          riskLevel: this.calculateRiskLevel(
            outcomePrediction.outcomeScore,
            complications,
          ),
          noShowProbability,
          treatmentDuration: outcomePrediction.timeline.totalDuration,
          complications,
          recoveryTimeline,
        },
        recommendations,
        confidence: model.accuracy,
        modelVersion: modelId,
        trainingDataDate: model.trainingDate,
      };

      return prediction;
    } catch (error) {
      console.error("Prediction error:", error);
      throw new Error(`Failed to generate prediction: ${error}`);
    }
  }

  /**
   * Extract relevant features for prediction
   */
  private async extractFeatures(
    patientData: Record<string, unknown>,
    treatmentId: string,
  ): Promise<Record<string, unknown>> {
    const features: Record<string, unknown> = {
      patient_age: patientData.age || 35,
      skin_type: patientData.skinType || "mixed",
      treatment_history: patientData.treatmentHistory?.length || 0,
      medical_conditions: patientData.medicalConditions?.length || 0,
      medication_usage: patientData.currentMedications?.length || 0,
      lifestyle_factors: {
        smoking: patientData.lifestyle?.smoking || false,
        alcohol: patientData.lifestyle?.alcohol || "moderate",
        exercise: patientData.lifestyle?.exercise || "regular",
        stress: patientData.lifestyle?.stressLevel || "moderate",
      },
      treatment_area: this.determineTreatmentArea(treatmentId),
      procedure_complexity: this.calculateProcedureComplexity(treatmentId),
      seasonal_factors: this.getSeasonalFactors(),
      compliance_history: patientData.complianceScore || 0.8,
      contraindications: patientData.contraindications?.length || 0,
      recovery_factors: {
        age_factor: this.calculateAgeFactor(patientData.age || 35),
        health_factor: this.calculateHealthFactor(patientData),
        lifestyle_factor: this.calculateLifestyleFactor(patientData.lifestyle),
      },
    };

    // Apply Brazilian-specific factors
    features.brazilian_factors = BRAZILIAN_HEALTH_FACTORS.reduce(
      (acc, factor) => {
        acc[factor.name] = this.evaluateBrazilianFactor(factor, patientData);
        return acc;
      },
      {} as Record<string, number>,
    );

    return features;
  }

  /**
   * Generate outcome prediction using ML model
   */
  private async generateOutcomePrediction(
    features: Record<string, unknown>,
    model: MLModel,
  ): Promise<OutcomePrediction> {
    // Simulate ML model prediction
    const baseScore = 0.75 + Math.random() * 0.2; // Base 75-95% success rate

    // Adjust based on features
    let adjustedScore = baseScore;

    // Age factor
    const ageFactor = features.patient_age < 30 ? 1.05 : features.patient_age > 60 ? 0.95 : 1;
    adjustedScore *= ageFactor;

    // Medical conditions impact
    const conditionsFactor = Math.max(
      0.85,
      1 - features.medical_conditions * 0.05,
    );
    adjustedScore *= conditionsFactor;

    // Compliance history impact
    adjustedScore *= features.compliance_history;

    // Brazilian factors impact
    const brazilianImpact = Object.values(features.brazilian_factors).reduce(
      (sum: number, value: unknown) => sum + (typeof value === "number" ? value : 0) * 0.1,
      0,
    ) / Object.keys(features.brazilian_factors).length;
    adjustedScore += brazilianImpact;

    // Clamp to valid range
    adjustedScore = Math.max(0.3, Math.min(0.98, adjustedScore));

    const prediction: OutcomePrediction = {
      treatmentId: "treatment-123",
      outcomeScore: adjustedScore * 100,
      predictionAccuracy: model.accuracy,
      factors: this.calculatePredictiveFactors(features),
      timeline: this.generateTreatmentTimeline(features),
      alternatives: await this.generateAlternativeTreatments(features),
      riskFactors: this.identifyRiskFactors(features),
      confidenceInterval: [
        (adjustedScore - 0.1) * 100,
        (adjustedScore + 0.1) * 100,
      ],
    };

    return prediction;
  }

  /**
   * Predict potential complications
   */
  private async predictComplications(
    features: Record<string, unknown>,
    patientData: Record<string, unknown>,
  ): Promise<ComplicationPrediction[]> {
    const complications: ComplicationPrediction[] = [];

    // Common aesthetic treatment complications
    const potentialComplications = [
      {
        type: "Inchaço leve",
        baseProbability: 0.25,
        severity: "minor" as const,
        timeframe: 3,
        factors: ["skin_type", "age", "treatment_area"],
      },
      {
        type: "Hiperpigmentação temporária",
        baseProbability: 0.15,
        severity: "minor" as const,
        timeframe: 14,
        factors: ["skin_type", "seasonal_factors"],
      },
      {
        type: "Reação alérgica",
        baseProbability: 0.08,
        severity: "moderate" as const,
        timeframe: 1,
        factors: ["medical_conditions", "medication_usage"],
      },
      {
        type: "Infecção localizada",
        baseProbability: 0.05,
        severity: "severe" as const,
        timeframe: 7,
        factors: ["compliance_history", "lifestyle_factors"],
      },
    ];

    potentialComplications.forEach((comp) => {
      let adjustedProbability = comp.baseProbability;

      // Adjust based on patient factors
      comp.factors.forEach((factor) => {
        if (features[factor]) {
          const impact = this.calculateFactorImpact(factor, features[factor]);
          adjustedProbability *= 1 + impact;
        }
      });

      // Only include complications with significant probability
      if (adjustedProbability > 0.05) {
        complications.push({
          type: comp.type,
          probability: Math.min(0.8, adjustedProbability), // Cap at 80%
          severity: comp.severity,
          timeframe: comp.timeframe,
          preventionStrategies: this.getPreventionStrategies(comp.type),
          warningSignals: this.getWarningSignals(comp.type),
        });
      }
    });

    return complications.sort((a, b) => b.probability - a.probability);
  }

  /**
   * Generate recovery timeline milestones
   */
  private generateRecoveryTimeline(
    features: Record<string, unknown>,
    prediction: OutcomePrediction,
  ) {
    const milestones = [
      {
        milestone: "Redução inicial do inchaço",
        expectedDay: 3,
        baseProbability: 0.92,
        dependencies: ["Cuidados pós-procedimento", "Repouso adequado"],
        criticalFactors: ["Hidratação", "Compressas frias"],
      },
      {
        milestone: "Retorno às atividades normais",
        expectedDay: 7,
        baseProbability: 0.85,
        dependencies: ["Ausência de complicações", "Seguimento das instruções"],
        criticalFactors: ["Evitar exercícios intensos", "Proteção solar"],
      },
      {
        milestone: "Resultado parcial visível",
        expectedDay: 14,
        baseProbability: 0.78,
        dependencies: ["Cicatrização adequada", "Cuidados contínuos"],
        criticalFactors: ["Regeneração celular", "Resposta individual"],
      },
      {
        milestone: "Resultado final esperado",
        expectedDay: prediction.timeline.totalDuration,
        baseProbability: prediction.outcomeScore / 100,
        dependencies: [
          "Todos os marcos anteriores",
          "Ausência de complicações",
        ],
        criticalFactors: ["Healing response", "Patient compliance"],
      },
    ];

    return milestones.map((m) => ({
      milestone: m.milestone,
      expectedDay: m.expectedDay,
      probability: Math.max(
        0.5,
        m.baseProbability * features.compliance_history,
      ),
      dependencies: m.dependencies,
      criticalFactors: m.criticalFactors,
    }));
  }

  /**
   * Predict no-show probability
   */
  private async predictNoShowProbability(patientData: Record<string, unknown>): Promise<number> {
    const model = this.models.get("neonpro-no-show-predictor-v1.5");
    if (!model) {
      return 0.12;
    } // Default fallback

    let baseProb = 0.12; // 12% base no-show rate

    // Adjust based on patient factors
    const factors = {
      age: patientData.age || 35,
      appointmentHistory: patientData.appointmentHistory || [],
      communicationPrefs: patientData.communicationPreferences || "sms",
      paymentMethod: patientData.paymentMethod || "card",
      distance: patientData.distanceToClinic || 10,
    };

    // Age factor (younger patients more likely to no-show)
    if (factors.age < 25) {
      baseProb *= 1.3;
    } else if (factors.age > 50) {
      baseProb *= 0.8;
    }

    // History factor
    const noShowHistory = factors.appointmentHistory.filter(
      (apt: Record<string, unknown>) => apt.status === "no_show",
    ).length;
    const totalAppointments = factors.appointmentHistory.length;
    if (totalAppointments > 0) {
      const historicalRate = noShowHistory / totalAppointments;
      baseProb = (baseProb + historicalRate) / 2;
    }

    // Distance factor
    if (factors.distance > 20) {
      baseProb *= 1.2;
    }

    return Math.min(0.6, Math.max(0.02, baseProb));
  }

  /**
   * Calculate risk level based on prediction and complications
   */
  private calculateRiskLevel(
    outcomeScore: number,
    complications: ComplicationPrediction[],
  ): "low" | "medium" | "high" | "critical" {
    const highRiskComplications = complications.filter(
      (c) =>
        c.severity === "severe"
        || c.severity === "critical"
        || c.probability > 0.3,
    );

    if (outcomeScore < 60 || highRiskComplications.length > 2) {
      return "critical";
    }
    if (outcomeScore < 75 || highRiskComplications.length > 1) {
      return "high";
    }
    if (outcomeScore < 85 || highRiskComplications.length > 0) {
      return "medium";
    }
    return "low";
  }

  /**
   * Generate AI-powered recommendations
   */
  private generateRecommendations(
    prediction: OutcomePrediction,
    complications: ComplicationPrediction[],
    features: Record<string, unknown>,
  ) {
    return {
      optimalTreatment: {
        id: "treatment-opt-1",
        name: "Protocolo Otimizado Baseado em IA",
        description: "Protocolo personalizado com base na análise preditiva",
        steps: [],
        duration: prediction.timeline.totalDuration,
        cost: 0,
        successProbability: prediction.outcomeScore / 100,
      },
      preventiveMeasures: this.generatePreventiveMeasures(
        complications,
        features,
      ),
      followUpSchedule: this.generateFollowUpSchedule(prediction),
      riskMitigation: this.generateRiskMitigation(complications),
      resourceAllocation: this.generateResourceRecommendations(features),
    };
  }

  // ====== HELPER METHODS ======

  private determineTreatmentArea(treatmentId: string): string {
    // Determine treatment area based on treatment ID
    if (treatmentId.includes("facial")) {
      return "face";
    }
    if (treatmentId.includes("body")) {
      return "body";
    }
    return "face"; // default
  }

  private calculateProcedureComplexity(treatmentId: string): number {
    // Calculate procedure complexity score (1-10)
    const complexityMap: Record<string, number> = {
      botox: 3,
      dermal_fillers: 5,
      laser_facial: 7,
      chemical_peel: 4,
      surgery: 10,
    };

    return complexityMap[treatmentId] || 5;
  }

  private getSeasonalFactors(): Record<string, number> {
    const month = new Date().getMonth();
    const season = month >= 2 && month <= 4
      ? "fall"
      : month >= 5 && month <= 7
      ? "winter"
      : month >= 8 && month <= 10
      ? "spring"
      : "summer";

    return {
      season_impact: season === "winter" ? 1.1 : season === "summer" ? 0.9 : 1,
      humidity: month >= 11 || month <= 3 ? 0.8 : 1.2, // Brazilian seasons
      uv_exposure: month >= 11 || month <= 3 ? 1.3 : 0.7,
    };
  }

  private calculateAgeFactor(age: number): number {
    // Age impact on healing (0.5-1.2 scale)
    if (age < 25) {
      return 1.2;
    }
    if (age < 35) {
      return 1.1;
    }
    if (age < 50) {
      return 1;
    }
    if (age < 65) {
      return 0.9;
    }
    return 0.7;
  }

  private calculateHealthFactor(patientData: Record<string, unknown>): number {
    let healthScore = 1;

    // Reduce based on medical conditions
    const conditions = patientData.medicalConditions || [];
    healthScore -= conditions.length * 0.1;

    // Reduce based on medications
    const medications = patientData.currentMedications || [];
    healthScore -= medications.length * 0.05;

    return Math.max(0.5, healthScore);
  }

  private calculateLifestyleFactor(lifestyle: Record<string, unknown>): number {
    if (!lifestyle) {
      return 1;
    }

    let factor = 1;

    if (lifestyle.smoking) {
      factor *= 0.8;
    }
    if (lifestyle.alcohol === "heavy") {
      factor *= 0.9;
    }
    if (lifestyle.exercise === "none") {
      factor *= 0.9;
    }
    if (lifestyle.stressLevel === "high") {
      factor *= 0.9;
    }

    return Math.max(0.6, factor);
  }

  private evaluateBrazilianFactor(
    factor: PredictiveFactor,
    patientData: Record<string, unknown>,
  ): number {
    // Evaluate Brazilian-specific factors
    switch (factor.name) {
      case "sus_integration":
        return patientData.susIntegrated ? 0.1 : 0;
      case "regional_climate":
        return this.getSeasonalFactors().season_impact - 1;
      case "cultural_compliance":
        return (patientData.complianceScore || 0.8) - 0.8;
      case "economic_accessibility":
        return patientData.hasInsurance ? 0.1 : -0.1;
      case "anvisa_compliance":
        return 0.15; // Assuming all treatments are ANVISA compliant
      default:
        return 0;
    }
  }

  private calculatePredictiveFactors(
    features: Record<string, unknown>,
  ): PredictiveFactor[] {
    return BRAZILIAN_HEALTH_FACTORS.map((factor) => ({
      ...factor,
      impact: this.evaluateBrazilianFactor(factor, features),
    }));
  }

  private generateTreatmentTimeline(
    features: Record<string, unknown>,
  ): TreatmentTimeline {
    const baseDuration = 21; // 21 days base recovery
    const ageFactor = features.recovery_factors?.age_factor || 1;
    const healthFactor = features.recovery_factors?.health_factor || 1;

    const adjustedDuration = Math.round(
      baseDuration * (2 - ageFactor) * (2 - healthFactor),
    );

    return {
      phases: [
        {
          name: "Immediate Post-Treatment",
          duration: 3,
          description: "Immediate recovery phase",
        },
        {
          name: "Initial Healing",
          duration: 7,
          description: "Primary healing phase",
        },
        {
          name: "Tissue Remodeling",
          duration: 14,
          description: "Tissue regeneration phase",
        },
        {
          name: "Final Results",
          duration: adjustedDuration,
          description: "Final outcome manifestation",
        },
      ],
      totalDuration: adjustedDuration,
      criticalMilestones: [
        { day: 3, milestone: "Initial swelling reduction", importance: "high" },
        {
          day: 7,
          milestone: "Return to normal activities",
          importance: "medium",
        },
        { day: 14, milestone: "Partial results visible", importance: "high" },
        {
          day: adjustedDuration,
          milestone: "Final results",
          importance: "critical",
        },
      ],
      flexibilityScore: Math.round((healthFactor + ageFactor) * 50),
    };
  }

  private async generateAlternativeTreatments(
    features: Record<string, unknown>,
  ): Promise<AlternativeTreatment[]> {
    // Generate alternative treatment suggestions
    return [
      {
        treatmentId: "alt-conservative",
        name: "Abordagem Conservadora",
        successProbability: 0.75,
        costDifference: -800,
        timeDifference: -5,
        riskProfile: { overallRisk: "low", complications: [] },
        suitabilityScore: 80,
      },
      {
        treatmentId: "alt-intensive",
        name: "Protocolo Intensivo",
        successProbability: 0.92,
        costDifference: 1200,
        timeDifference: 10,
        riskProfile: { overallRisk: "medium", complications: [] },
        suitabilityScore: 75,
      },
    ];
  }

  private identifyRiskFactors(features: Record<string, unknown>): string[] {
    const riskFactors: string[] = [];

    if (features.patient_age > 60) {
      riskFactors.push("Idade avançada");
    }
    if (features.medical_conditions > 2) {
      riskFactors.push("Múltiplas condições médicas");
    }
    if (features.compliance_history < 0.7) {
      riskFactors.push("Histórico de baixa aderência");
    }
    if (features.lifestyle_factors?.smoking) {
      riskFactors.push("Tabagismo");
    }
    if (features.contraindications > 0) {
      riskFactors.push("Contraindicações presentes");
    }

    return riskFactors;
  }

  private calculateFactorImpact(factor: string, value: unknown): number {
    // Calculate how much a factor impacts complication probability
    const impactMap: Record<string, (val: unknown) => number> = {
      skin_type: (val) => (val === "sensitive" ? 0.2 : 0),
      age: (val) => (val > 60 ? 0.15 : val < 25 ? 0.1 : 0),
      medical_conditions: (val) => val * 0.1,
      compliance_history: (val) => (1 - val) * 0.3,
    };

    return impactMap[factor] ? impactMap[factor](value) : 0;
  }

  private getPreventionStrategies(complicationType: string): string[] {
    const strategies: Record<string, string[]> = {
      "Inchaço leve": [
        "Compressas frias",
        "Elevação da área",
        "Anti-inflamatórios",
      ],
      "Hiperpigmentação temporária": [
        "Protetor solar FPS 50+",
        "Evitar exposição solar",
        "Hidratação",
      ],
      "Reação alérgica": [
        "Teste de alergia prévio",
        "Anti-histamínicos",
        "Monitoramento",
      ],
      "Infecção localizada": [
        "Higiene rigorosa",
        "Antibiótico profilático",
        "Cuidados assépticos",
      ],
    };

    return (
      strategies[complicationType] || [
        "Monitoramento regular",
        "Seguir instruções médicas",
      ]
    );
  }

  private getWarningSignals(complicationType: string): string[] {
    const signals: Record<string, string[]> = {
      "Inchaço leve": [
        "Inchaço excessivo",
        "Dor crescente",
        "Vermelhidão intensa",
      ],
      "Hiperpigmentação temporária": [
        "Escurecimento da pele",
        "Manchas irregulares",
      ],
      "Reação alérgica": [
        "Coceira intensa",
        "Urticária",
        "Dificuldade respiratória",
      ],
      "Infecção localizada": ["Secreção purulenta", "Febre", "Dor pulsátil"],
    };

    return (
      signals[complicationType] || ["Sintomas persistentes", "Piora do quadro"]
    );
  }

  private generatePreventiveMeasures(
    complications: ComplicationPrediction[],
    features: Record<string, unknown>,
  ) {
    const measures = [];

    // High-priority universal measures
    measures.push({
      id: "sun-protection",
      action: "Aplicar protetor solar FPS 50+ diariamente",
      priority: "critical" as const,
      timeframe: "Durante todo o tratamento",
      importance: 0.98,
    });

    // Complication-specific measures
    complications.forEach((comp) => {
      comp.preventionStrategies.forEach((strategy, index) => {
        measures.push({
          id: `${comp.type.toLowerCase()}-prev-${index}`,
          action: strategy,
          priority: comp.severity === "severe"
            ? ("critical" as const)
            : comp.severity === "moderate"
            ? ("high" as const)
            : ("medium" as const),
          timeframe: `${comp.timeframe} dias`,
          importance: comp.probability * 0.9,
        });
      });
    });

    return measures.slice(0, 5); // Return top 5 measures
  }

  private generateFollowUpSchedule(prediction: OutcomePrediction) {
    return {
      appointments: [
        {
          day: 3,
          type: "check-up",
          importance: "high",
          description: "Avaliação inicial pós-procedimento",
        },
        {
          day: 7,
          type: "follow-up",
          importance: "medium",
          description: "Verificação da cicatrização",
        },
        {
          day: 14,
          type: "assessment",
          importance: "high",
          description: "Análise dos resultados parciais",
        },
        {
          day: prediction.timeline.totalDuration,
          type: "final",
          importance: "critical",
          description: "Avaliação final dos resultados",
        },
      ],
    };
  }

  private generateRiskMitigation(complications: ComplicationPrediction[]) {
    return complications.map((comp) => ({
      risk: comp.type,
      strategy: comp.preventionStrategies[0] || "Monitoramento regular",
      probability: Math.min(0.95, 0.8 + comp.probability * 0.3), // Effectiveness probability
      impact: comp.severity === "critical"
        ? ("high" as const)
        : comp.severity === "severe"
        ? ("high" as const)
        : comp.severity === "moderate"
        ? ("medium" as const)
        : ("low" as const),
    }));
  }

  private generateResourceRecommendations(features: Record<string, unknown>) {
    const recommendations = [];

    // Standard recommendations
    recommendations.push({
      resource: "Tempo de consulta",
      recommended: "45 minutos",
      rationale: "Paciente requer explicações detalhadas sobre cuidados pós-procedimento",
    });

    // Age-based recommendations
    if (features.patient_age > 60) {
      recommendations.push({
        resource: "Follow-up adicional",
        recommended: "1 consulta extra",
        rationale: "Idade avançada requer monitoramento mais frequente",
      });
    }

    // Compliance-based recommendations
    if (features.compliance_history < 0.8) {
      recommendations.push({
        resource: "Suporte educacional",
        recommended: "Material educativo + lembretes SMS",
        rationale: "Histórico de baixa aderência necessita reforço educacional",
      });
    }

    return recommendations;
  }

  /**
   * Get available prediction models
   */
  getAvailableModels(): MLModel[] {
    return Array.from(this.models.values());
  }

  /**
   * Get model accuracy and performance metrics
   */
  getModelMetrics(modelId: string): Record<string, unknown> | null {
    const model = this.models.get(modelId);
    return model
      ? {
        accuracy: model.accuracy,
        precision: model.precision,
        recall: model.recall,
        f1Score: model.f1Score,
        lastUpdated: model.lastRetraining,
      }
      : null;
  }

  /**
   * Update model with new training data
   */
  async retrainModel(modelId: string, trainingData: Record<string, unknown>[]): Promise<boolean> {
    try {
      const model = this.models.get(modelId);
      if (!model) {
        return false;
      }

      // Simulate model retraining
      model.lastRetraining = new Date();
      model.trainingDataSize += trainingData.length;

      // Slight accuracy improvement with more data
      model.accuracy = Math.min(0.98, model.accuracy + 0.005);

      return true;
    } catch (error) {
      console.error("Model retraining failed:", error);
      return false;
    }
  }
}

// ====== EXPORT SERVICE INSTANCE ======

export const predictiveModels = new PredictiveModelsService();
